package se.visionmate.vcommerce.core

import grails.gorm.transactions.Transactional
import grails.util.Environment
import org.apache.commons.validator.routines.UrlValidator
import org.hibernate.Session
import se.visionmate.vcommerce.api.FtpService
import se.visionmate.vcommerce.api.VismaConnectService
import se.visionmate.vcommerce.enums.FileExtension
import se.visionmate.vcommerce.enums.PacketOption

class AttachmentService {

    AppService appService
    VismaConnectService vismaConnectService
    FtpService ftpService
    UpdateService updateService
    PacketService packetService

    /**
     * Save attachments to the local DB
     * @param attachments as List
     * @param product as Product
     * @param canUpload as Boolean(optional)
     */
    @Transactional
    void populateAttachments(List attachments, Product product, Boolean canUpload = false) {
        Attachment attachment
        FileExtension extension
        Map data = [:]
        attachments.eachWithIndex{ attach, idx ->
            extension = getFileExtension(attach.name as String)
            if (isFile(extension) && !packetService.isActivePacketOption(PacketOption.FILES)) {
                println "Packet '${PacketOption.FILES}' is inactive"
            } else {
                data.name = extension
                        ? getAttachmentName(attach.name as String, attach.id as String, attach.revision as Integer, extension)
                        : null
                data.originName = attach.name
                data.vismaId = attach.id
                data.revision = attach.revision
                data.position = idx

                attachment = Attachment.findByVismaId(data.vismaId as String)
                if (!attachment) {
                    println 'Creating a new attachment'
                    attachment = createNewAttachment(data, product, extension)
                    if (attachment && canUpload) {
                        uploadAttachmentViaFtp(attachment)
                    }
                } else {
                    println "[${new Date().format('yyyy-MM-dd HH:mm:ss')}] $attachment - already exists"
                }
            }
        }
    }

    /**
     * Creating a new attachment
     * @param data as Map
     * @param extension as FileExtension
     * @param flush as Bool(optional)
     * @return new Attachment
     */
    Attachment createNewAttachment(Map data, Product product, FileExtension extension, Boolean flush = false) {
        Attachment attachment = new Attachment(
                name: data.name,
                originName: data.originName,
                vismaId: data.vismaId,
                revision: data.revision as Integer,
                position: data.position as Integer,
                extension: extension,
                product: product,
                uploaded: false
        )
        if (attachment.validate()){
            if (attachment.save(flush: flush)) {
                println "[${new Date().format('yyyy-MM-dd HH:mm:ss')}] $attachment - has been created"
                return attachment
            } else {
                println 'An error occurred while creating a new attachment'
                if (appService.debugMode) {
                    attachment.errors.allErrors.each { println it }
                }
                return null
            }
        } else {
            println 'Validation issue occurred while creating a new attachment'
            if (Environment.current == Environment.DEVELOPMENT) {
                attachment.errors.allErrors.each { println it }
            }
            return null
        }
    }

    /**
     * Upload an attachment via FTP
     * @param attachmentName as String
     * @param attachmentId as String
     * @return bool
     */
    Boolean uploadAttachmentViaFtp(String attachmentName, String attachmentId) {
        File tmpFile = vismaConnectService.getAttachment(attachmentId)
        if (tmpFile) {
            println "FTP: Uploading $attachmentName to Woo"
            String result = ftpService.upload(attachmentName, new FileInputStream(tmpFile))
            println result
            return result.trim().startsWith('226') ?: false
        } else {
            println "Something went wrong while downloading the file"
            return false
        }
    }

    /**
     * Upload an attachment via FTP
     * @param attachment as Attachment
     */
    void uploadAttachmentViaFtp(Attachment attachment) {
        if (uploadAttachmentViaFtp(attachment.name, attachment.vismaId)) {
            attachment.uploaded = true
            attachment.save(flush: true)
        } else {
            throw new Exception("FTP-ERROR: while uploading $attachment")
        }
    }

    /**
     * Upload all not uploaded images via FTP
     */
    void uploadImagesViaFtp() {
        List<Attachment> images = Attachment.list().findAll { Attachment attachment ->
            !attachment.uploaded && attachment.extension == FileExtension.JPG ||
            !attachment.uploaded && attachment.extension == FileExtension.PNG
        }
        if (images) {
            images.each {Attachment image ->
                if (uploadAttachmentViaFtp(image.name, image.vismaId)) {
                    image.uploaded = true
                    image.save(flush: true)
                }
            }
        }
    }

    void uploadFilesViaFtp() {
        List<Attachment> files = Attachment.list().findAll { Attachment attachment ->
            !attachment.uploaded && attachment.isFile()
        }
        if (files) {
            files.each {Attachment file ->
                if (uploadAttachmentViaFtp(file.name, file.vismaId)) {
                    file.uploaded = true
                    file.save(flush: true)
                }
            }
        }
    }

    /**
     * Get file extension by file name
     * @param fileName as String
     * @return extension as FileExtension
     */
    FileExtension getFileExtension(String fileName) {
        if (!fileName) { return null }
        FileExtension result
        switch (fileName.trim().toLowerCase()) {
            case ~/^.*\.jpg$/:
                result = FileExtension.JPG
                break
            case ~/^.*\.jpeg$/:
                result = FileExtension.JPG
                break
            case ~/^.*\.png$/:
                result = FileExtension.PNG
                break
            case ~/^.*\.pdf$/:
                result = FileExtension.PDF
                break
            case ~/^.*\.doc$/:
                result = FileExtension.DOC
                break
            case ~/^.*\.docx$/:
                result = FileExtension.DOC
                break
            case ~/^.*\.xls$/:
                result = FileExtension.XLS
                break
            case ~/^.*\.xlsx$/:
                result = FileExtension.XLS
                break
            default:
                result = FileExtension.UNDEFINED
                break
        }
        result
    }

    /**
     * Updating wooId for images
     * @param product as Product
     * @param wooImages as List of wooImgObj
     */
    @Transactional
    void updateWooId(Product product, List wooImages) {
        Attachment attachment
        wooImages.each { def wooImg ->
            attachment = product.attachments.find { it.name == wooImg.name && it.wooId == null }
            if (attachment) {
                attachment.wooId = wooImg.id
                attachment.save()
                log.info("Updated wooId for attachment '$attachment': ${attachment.wooId}")
            }
        }
//        Attachment.withSession {
//            it.flush()
//            it.clear()
//        }
    }

    /**
     * Checking attachments for updates
     * @param product as Product
     * @param attachments as List
     * @return Boolean
     */
    @Transactional
    Boolean attachmentsUpdated(Product product, List attachments) {
        Boolean updated = false
        Attachment attachment
        FileExtension extension
        List<Attachment> processedAttachments = []
        Map data = [:]

        if (attachments) {
            attachments.eachWithIndex { attachVismaObj, idx ->
                attachment = product.attachments.find { it.vismaId == attachVismaObj.id }
                if (!attachment) {
                    extension = getFileExtension(attachVismaObj.name as String)
                    if (isFile(extension) && !packetService.isActivePacketOption(PacketOption.FILES)) {
                        println "Packet '${PacketOption.FILES}' is inactive"
                    } else {
                        data.name = extension
                                ? getAttachmentName(attachVismaObj.name as String, attachVismaObj.id as String, attachVismaObj.revision as Integer, extension)
                                : null
                        data.originName = attachVismaObj.name
                        data.vismaId = attachVismaObj.id
                        data.revision = attachVismaObj.revision
                        data.position = idx
                        attachment = createNewAttachment(data, product, extension, true)
                        if (attachment) {
                            uploadAttachmentViaFtp(attachment)
                            product.addToAttachments(attachment)
                            processedAttachments << attachment
                            updated = true
                        }
                    }
                } else {
                    if (updateService.fieldUpdated(attachment, 'revision', attachVismaObj.revision)) {
                        updated = true
                        if (isImage(attachment.extension)) {
                            updateAttachmentName(attachment)
                        }
                        uploadAttachmentViaFtp(attachment)
                    }
                    if (!attachment.originName) {
                        attachment.originName = attachVismaObj.name
                    }
                    updated = updateService.fieldUpdated(attachment, 'position', idx) || updated
                    attachment.save(flush: true)
                    processedAttachments << attachment
                }
            }
        }

        if (product.attachments.size() != processedAttachments.size()) {
            product.attachments.each { Attachment attach ->
                attachment = processedAttachments.find { it.vismaId == attach.vismaId }
                if (!attachment) {
                    product.refresh()
                    product.removeFromAttachments(attach)
                    attach.delete()
                    product.save(flush: true)
                    updated = true
                }
            }
        }

        return updated
    }

    /**
     * Get an attachment name depending on extension
     * @param originName as String
     * @param vismaId as String
     * @param extension as FileExtension
     * @return
     */
    String getAttachmentName(String originName, String vismaId, Integer revision, FileExtension extension) {
        switch (extension) {
            case FileExtension.JPG:
                return "${vismaId}_rev${revision}.jpg"
            case FileExtension.PNG:
                return "${vismaId}_rev${revision}.png"
            case FileExtension.PDF:
            case FileExtension.DOC:
            case FileExtension.XLS:
                return getDocumentName(originName)
            default:
                return null
        }
    }

    /**
     * Get document name
     * @param docName as String
     * @return
     */
    String getDocumentName(String docName) {
        if (isURL(docName)) {
            docName.substring(docName.lastIndexOf('/') + 1)
        } else {
            clearAttachmentName(docName)
        }
    }

    /**
     * Checking if the string is url using the default scheme [http, https, ftp]
     * @param str as String
     * @return bool
     */
    Boolean isURL(String str) {
        UrlValidator.getInstance().isValid(str)
    }

    /**
     * Checking if attachment is image by extension
     * @param extension as FileExtension
     * @retun bool
     */
    Boolean isImage(FileExtension extension) {
        extension == FileExtension.JPG || extension == FileExtension.PNG
    }

    /**
     * Checking if attachment is file by extension
     * @param extension as FileExtension
     * @retun bool
     */
    Boolean isFile(FileExtension extension) {
        extension == FileExtension.DOC || extension == FileExtension.PDF || extension == FileExtension.XLS
    }

    /**
     * Updating the attachment name
     * @param attachment as Attachment
     */
    @Transactional
    void updateAttachmentName(Attachment attachment) {
        switch (attachment.extension) {
            case FileExtension.JPG:
                attachment.name = "${attachment.vismaId}_rev${attachment.revision}.jpg"
                break
            case FileExtension.PNG:
                attachment.name = "${attachment.vismaId}_rev${attachment.revision}.png"
                break
            case FileExtension.PDF:
            case FileExtension.DOC:
            case FileExtension.XLS:
                attachment.name = getDocumentName(attachment.originName)
                break
        }
    }

    /**
     * Prevent all incompatible characters from attachment name
     * @param name as String
     * @return
     */
    String clearAttachmentName(String name) {
        name.replaceAll("[^\\w.-]", "_")
    }

    /**
     * Fixing attachment names
     * @param attachments as List
     */
    void fixAttachmentNames(List<Attachment> attachments) {
        attachments.each {
            it.name = getAttachmentName(it.originName, it.vismaId, it.revision, it.extension)
        }
        Attachment.withSession { Session session ->
            session.flush()
            session.clear()
        }
    }
}
